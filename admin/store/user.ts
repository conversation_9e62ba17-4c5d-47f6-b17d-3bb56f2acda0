import { defineStore } from "pinia";
interface UserInfo {
  name: string;
  email: string;
  image: string;
  phone: string;
}
export const useLoginStore = defineStore("login", {
  state: () => ({
    token: "",
    userInfo: {} as UserInfo,
  }),
  getters: {
    isLoggedIn: (state) => !!state.token.length,
  },
  actions: {
    login(token: string) {
      this.token = token;
      // Navigation is handled by the calling component
      // connectToSocket();
    },
    logout() {
      this.token = "";
      this.userInfo = {} as UserInfo;
      // disconnectFromSocket();
      // Navigate to login page
      navigateTo("/login");
    },
    setUser(user: UserInfo) {
      this.userInfo = user;
    },
  },
  persist: true,
});
