import {
  CalendarDaysIcon,
  CameraIcon,
  ChartPieIcon,
  CurrencyDollarIcon,
  UserCircleIcon,
  WrenchScrewdriverIcon,
  ChartBarIcon,
} from "@heroicons/vue/24/solid";
import { defineStore } from "pinia";
import { computed } from "vue";
export type Routes = {
  to: string;
  label: string;
  icon: typeof CurrencyDollarIcon | string;
};
const routes = computed(() => {
  // Check if user is super admin
  const isSuperAdmin = process.client && localStorage.getItem('super_admin_token');

  const routes: Routes[] = [];

  if (isSuperAdmin) {
    // Super admin routes
    routes.push(
      {
        to: "/analytics/overview",
        label: "Analytics",
        icon: ChartBarIcon,
      },
      {
        to: "/profissionais",
        label: "Profissionais",
        icon: UserCircleIcon,
      },
      {
        to: "/pacientes",
        label: "Pacientes",
        icon: UserCircleIcon,
      },
      {
        to: "/solicitacoes",
        label: "Solicitações",
        icon: CurrencyDollarIcon,
      }
    );
  } else {
    // Regular professional routes
    routes.push(
      {
        to: "/atendimento",
        label: "Atendimento",
        icon: WrenchScrewdriverIcon,
      },
      {
        to: "/agenda",
        label: "Agenda",
        icon: CalendarDaysIcon,
      },
      {
        to: "/historico",
        label: "Histórico",
        icon: CurrencyDollarIcon,
      },
      {
        to: "/feed",
        label: "Feed",
        icon: CameraIcon,
      },
      {
        to: "/dashboard",
        label: "Faturamento",
        icon: ChartPieIcon,
      },
      {
        to: "/perfil",
        label: "Perfil",
        icon: UserCircleIcon,
      }
    );
  }

  return routes;
});
export const useNavigationBottom = defineStore("navigationBottom", {
  state: () => ({
    active: false,
    routes,
  }),
  actions: {
    toggleActive() {
      this.active = !this.active;
    },
  },
  persist: true,
});
