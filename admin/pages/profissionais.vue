<template>
  <div class="w-full">
    <div class="container-table pt-1 lg:h-sm">
      <table-base
        :total-items
        title="Barbeiros"
        :columns="columns"
        hide-delete
        :rows="leads"
        v-model:per_page="per_page"
        v-model:page="page"
        @edit="handleEdit"
        @delete="deleteUser"
        @new="
          () => {
            isOpened = true;
            isEditing = false;
          }
        "
        :loading="loading"
      >
        <template #src="{ value }">
          <td>
            <nuxt-img
              class="w-10 h-10 rounded-full"
              :src="
                value ||
                'https://static.vecteezy.com/system/resources/previews/019/896/008/original/male-user-avatar-icon-in-flat-design-style-person-signs-illustration-png.png'
              "
              alt="Profile image"
            />
          </td>
        </template>
        <template #filter>
          <div class="flex gap-4 items-center">
            <input-base
              type="text"
              label="Buscar"
              label-style="!-mb-2 ml-2 z-10 text-xs"
              class="sm:min-w-52"
              v-model="filters.name"
            />
            <input-base
              label="Ordenar por"
              class="hidden sm:flex"
              label-style="!-mb-2 ml-2 z-10 text-xs"
              v-model="filters.sort_by"
              :options="[{ value: 'name', label: 'Nome' }]"
            />
            <input-base
              label="Ordem"
              class="hidden sm:flex"
              label-style="!-mb-2 ml-2 z-10 text-xs"
              v-model="filters.direction"
              :options="[
                { value: 'asc', label: 'Crescente' },
                { value: 'desc', label: 'Decrescente' },
              ]"
            />
            <div class="sm:hidden">
              <div class="text-xs -mt-1">Filtros</div>
              <base-button
                @click="filterModalOpen = true"
                size="sm"
                class="mr-3 btn-circle"
              >
                <AdjustmentsHorizontalIcon class="w-5" />
              </base-button>
            </div>
          </div>
        </template>
        <template #additional-actions="{ item }">
          <li
            v-if="item.phone !== user.phone"
            @click="
              () => {
                selectedUser = item;
                finishBondModalOpen = true;
              }
            "
          >
            <a>Finalizar Vínculo</a>
          </li>
        </template>
        <template #created_at="{ value }">
          {{ dayjs(value).format("DD/MM/YYYY") }}
        </template>
      </table-base>
    </div>

    <base-dialog
      :title="
        selectedUser ? 'Editar profissional' : 'Cadastrar novo profissional'
      "
      v-model="isOpened"
      @close="resetFormValues"
    >
      <form v-if="isOpened" @submit.prevent="onSubmit" class="p-4">
        <div class="flex items-end gap-2 mb-2">
          <input-base
            type="text"
            label="Telefone *"
            v-model="phone"
            :readonly="!!(!userId && selectedUser)"
            data-maska="(##) #####-####"
            :error="errors.phone"
          />
          <base-button
            v-if="!selectedUser"
            @click="searchUser"
            size="sm"
            type="button"
            class="!h-[39px] !w-[39px] min-h-0"
          >
            <MagnifyingGlassIcon class="w-5" />
          </base-button>
        </div>

        <div
          v-if="userSearched || selectedUser || selectedUser"
          class="space-y-4"
        >
          <input-base
            type="text"
            label="Nome *"
            v-model="name"
            :readonly="userId || selectedUser"
            :error="errors.name"
          />
          <input-base
            type="email"
            label="Email"
            v-model="email"
            :readonly="userId || selectedUser"
            :error="errors.email"
          />

          <template v-if="!selectedUser && !userId">
            <input-base
              type="password"
              label="Senha *"
              v-model="password"
              :error="errors.password"
            />
            <input-base
              type="password"
              label="Confirmar Senha *"
              v-model="passwordConfirmation"
              :error="errors.passwordConfirmation"
            />
          </template>

          <!-- <input-base
            type="number"
            :min="0"
            :max="100"
            data-maska="###"
            label="Comissão (%)"
            v-model="commission"
            :error="errors.commission"
          /> -->
        </div>

        <base-button
          v-if="userSearched || selectedUser"
          type="submit"
          class="w-full mt-4"
          :loading="loading"
        >
          {{
            selectedUser
              ? "Salvar"
              : userId
                ? "Vincular Profissional"
                : "Cadastrar Profissional"
          }}
        </base-button>
      </form>
    </base-dialog>

    <base-dialog title="Filtros" v-model="filterModalOpen">
      <div class="flex flex-col gap-3">
        <input-base
          type="text"
          label="Buscar"
          label-style="!-mb-2 ml-2 z-10 text-xs"
          class="sm:min-w-52"
          v-model="filters.name"
        />
        <input-base
          label="Ordenar por"
          label-style="!-mb-2 ml-2 z-10 text-xs"
          v-model="filters.sort_by"
          :options="[{ value: 'name', label: 'Nome' }]"
        />
        <input-base
          label="Ordem"
          label-style="!-mb-2 ml-2 z-10 text-xs"
          v-model="filters.direction"
          :options="[
            { value: 'asc', label: 'Crescente' },
            { value: 'desc', label: 'Decrescente' },
          ]"
        />
        <base-button @click="handleFilter" class="w-full">Buscar</base-button>
      </div>
    </base-dialog>

    <base-dialog title="Finalizar Vínculo" v-model="finishBondModalOpen">
      <div class="p-4">
        <p class="mb-4">
          Tem certeza que deseja finalizar o vínculo com este profissional?
        </p>
        <div class="flex justify-end gap-2">
          <base-button variant="outline" @click="finishBondModalOpen = false"
            >Cancelar</base-button
          >
          <base-button variant="danger" @click="finishBond" :loading="loading"
            >Confirmar</base-button
          >
        </div>
      </div>
    </base-dialog>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store/users";
import {
  AdjustmentsHorizontalIcon,
  MagnifyingGlassIcon,
} from "@heroicons/vue/24/solid";
import dayjs from "dayjs";
import { useLoginStore } from "@/store/user";
import { useToast } from "vue-toast-notification";
import { useForm } from "vee-validate";
import * as yup from "yup";
import { type User } from "~/models/user";
import { api } from "~/server/api";

const userSearched = ref(false);
const selectedUser = ref<User>();
const userId = ref();

const schema = yup.object({
  phone: yup.string().required("Telefone é obrigatório"),
  name: yup.string().required("Nome é obrigatório"),
  email: yup
    .string()
    .email("Email inválido")
    .when(([], schema) => {
      if (!userId.value || (userId.value && !name.value.length))
        return schema.required("O email é obrigatório");
    }),
  password: yup.string().when(([], schema) => {
    if (!userId.value)
      return schema
        .required("Senha é obrigatória")
        .min(8, "Senha deve ter no mínimo 8 caracteres");
  }),
  passwordConfirmation: yup.string().when(([], schema) => {
    if (!userId.value)
      return schema
        .required("Confirmação de senha é obrigatória")
        .oneOf([yup.ref("password")], "Senhas não conferem");
  }),
  // commission: yup
  //   .number()
  //   .required("Comissão é obrigatória")
  //   .min(0, "Comissão deve ser maior que 0")
  //   .max(100, "Comissão deve ser no máximo 100")
  //   .typeError("Comissão deve ser no minimo 0"),
});

const user = useLoginStore().userInfo;
const { handleSubmit, resetForm, defineField, errors, setValues } = useForm({
  validationSchema: schema,
  initialValues: {
    password: "",
    passwordConfirmation: "",
  },
  validateOnMount: false,
});

const isEditing = ref(false);
const [phone] = defineField("phone");
const [name] = defineField("name");
const [email] = defineField("email");
const [password] = defineField("password");
const [passwordConfirmation] = defineField("passwordConfirmation");
const [commission] = defineField("commission");

const filterModalOpen = ref(false);
const finishBondModalOpen = ref(false);
const per_page = ref(10);
const page = ref(1);
const toast = useToast();
const isOpened = ref(false);
const selectedType = ref("admins");
const userStore = useUserStore();
const leads = ref<User[]>([]);
const loading = ref(false);
const totalItems = ref(0);

const filters = ref({
  name: "",
  sort_by: "name",
  direction: "asc",
});

const columns = [
  // { label: "Avatar", key: "src", sm: true },
  { label: "Nome", key: "name", sm: true },
  { label: "Email", key: "email" },
  // { label: "Criado em", key: "created_at" },
];

function resetFormValues() {
  userSearched.value = false;
  userId.value = null;
  selectedUser.value = undefined;
  resetForm();
}

async function searchUser() {
  if (!phone.value) return;
  try {
    loading.value = true;
    const { data } = await api.get("/search-user", {
      params: { phone: phone.value },
    });
    userSearched.value = true;

    if (data) {
      name.value = data.name;
      email.value = data.email;
      userId.value = data.id;
    } else {
      name.value = "";
      email.value = "";
      userId.value = null;
    }
  } catch (err) {
    console.error(err);
    toast.error("Erro ao buscar usuário");
  } finally {
    loading.value = false;
  }
}

const onSubmit = handleSubmit(async (values) => {
  try {
    loading.value = true;
    if (selectedUser.value) {
      await api.put(`/professionals/${selectedUser.value.id}`, {
        commission: values.commission,
      });
      toast.success("Profissional atualizado com sucesso!");
    } else if (userId.value) {
      await api.post("/vinculate-professional", {
        ...values,
        user_id: userId.value,
        commission: 100,
      });
      toast.success("Profissional vinculado com sucesso!");
    } else {
      await api.post("/create-professional", { ...values, commission: 100 });
      toast.success("Profissional cadastrado com sucesso!");
    }
    isOpened.value = false;
    resetFormValues();
    await getUsers();
  } catch (err) {
    console.error(err);
    toast.error(
      selectedUser.value
        ? "Erro ao atualizar profissional!"
        : userId.value
          ? "Erro ao vincular profissional!"
          : "Erro ao cadastrar profissional!"
    );
  } finally {
    loading.value = false;
  }
});

async function finishBond() {
  if (!selectedUser.value) return;

  try {
    loading.value = true;
    await api.post(`/finish-professional-bond/${selectedUser.value.id}`);
    toast.success("Vínculo finalizado com sucesso!");
    finishBondModalOpen.value = false;
    await getUsers();
  } catch (err) {
    console.error(err);
    toast.error("Erro ao finalizar vínculo!");
  } finally {
    loading.value = false;
    selectedUser.value = undefined;
  }
}

function handleFilter() {
  filterModalOpen.value = false;
  getUsers();
}

async function getUsers() {
  try {
    loading.value = true;
    const { data } = await api("/professionals", {
      params: {
        name: filters.value.name,
        sort_by: filters.value.sort_by,
        direction: filters.value.direction,
      },
    });
    leads.value = data.data;
    totalItems.value = data.total;
  } catch (error) {
    toast.error("Erro ao carregar profissionais!");
  } finally {
    loading.value = false;
  }
}

function handleEdit(user: User) {
  selectedUser.value = user;
  setValues({
    name: user.name,
    email: user.email,
    phone: user.phone,
    commission: user.commission,
  });
  // commission.value = user.commission;
  isEditing.value = true;
  isOpened.value = true;
}

async function deleteUser(user: User) {
  try {
    loading.value = true;
    await userStore.deleteUser("/" + selectedType.value, user.id);
    toast.warning("Profissional deletado!");
    await getUsers();
  } catch (error) {
    toast.error("Erro ao deletar profissional!");
  } finally {
    loading.value = false;
  }
}

const debouncedFetch = () => useDelay(async () => await getUsers(), 500);

watch(
  filters,
  () => {
    if (filterModalOpen.value) return;
    if (filters.value.name) {
      debouncedFetch();
    } else {
      getUsers();
    }
  },
  { deep: true }
);

watch(isOpened, () => {
  if (!isOpened.value) {
    resetFormValues();
  }
});

onMounted(getUsers);
</script>
