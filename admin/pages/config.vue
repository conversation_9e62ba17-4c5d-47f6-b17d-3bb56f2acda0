<template>
  <div class="p-6">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-2xl font-bold mb-6">Configurações do Sistema</h1>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- System Settings -->
        <div class="bg-white rounded-lg shadow p-6">
          <h2 class="text-lg font-semibold mb-4">Configurações Gerais</h2>
          
          <form @submit.prevent="updateSystemSettings" class="space-y-4">
            <input-base
              v-model="systemSettings.site_name"
              label="Nome do Site"
              :error="errors.site_name"
            />
            <input-base
              v-model="systemSettings.admin_email"
              label="Email do Administrador"
              type="email"
              :error="errors.admin_email"
            />
            <textarea-base
              v-model="systemSettings.site_description"
              label="Descrição do Site"
              :error="errors.site_description"
            />
            
            <div class="flex items-center gap-2">
              <input
                type="checkbox"
                id="maintenance_mode"
                v-model="systemSettings.maintenance_mode"
                class="checkbox checkbox-primary"
              />
              <label for="maintenance_mode" class="text-sm">
                Modo de Manutenção
              </label>
            </div>
            
            <base-button type="submit" :loading="loadingSystem">
              Salvar Configurações
            </base-button>
          </form>
        </div>
        
        <!-- Email Settings -->
        <div class="bg-white rounded-lg shadow p-6">
          <h2 class="text-lg font-semibold mb-4">Configurações de Email</h2>
          
          <form @submit.prevent="updateEmailSettings" class="space-y-4">
            <input-base
              v-model="emailSettings.smtp_host"
              label="Servidor SMTP"
              :error="errors.smtp_host"
            />
            <input-base
              v-model="emailSettings.smtp_port"
              label="Porta SMTP"
              type="number"
              :error="errors.smtp_port"
            />
            <input-base
              v-model="emailSettings.smtp_username"
              label="Usuário SMTP"
              :error="errors.smtp_username"
            />
            <input-base
              v-model="emailSettings.smtp_password"
              label="Senha SMTP"
              type="password"
              :error="errors.smtp_password"
            />
            
            <div class="flex items-center gap-2">
              <input
                type="checkbox"
                id="smtp_encryption"
                v-model="emailSettings.smtp_encryption"
                class="checkbox checkbox-primary"
              />
              <label for="smtp_encryption" class="text-sm">
                Usar Criptografia SSL/TLS
              </label>
            </div>
            
            <base-button type="submit" :loading="loadingEmail">
              Salvar Email
            </base-button>
          </form>
        </div>
        
        <!-- Analytics Settings -->
        <div class="bg-white rounded-lg shadow p-6">
          <h2 class="text-lg font-semibold mb-4">Configurações de Analytics</h2>
          
          <form @submit.prevent="updateAnalyticsSettings" class="space-y-4">
            <input-base
              v-model="analyticsSettings.google_analytics_id"
              label="Google Analytics ID"
              :error="errors.google_analytics_id"
            />
            <textarea-base
              v-model="analyticsSettings.google_service_account"
              label="Google Service Account JSON"
              placeholder="Cole aqui o JSON da conta de serviço do Google"
              :error="errors.google_service_account"
            />
            
            <div class="flex items-center gap-2">
              <input
                type="checkbox"
                id="analytics_enabled"
                v-model="analyticsSettings.enabled"
                class="checkbox checkbox-primary"
              />
              <label for="analytics_enabled" class="text-sm">
                Habilitar Analytics
              </label>
            </div>
            
            <base-button type="submit" :loading="loadingAnalytics">
              Salvar Analytics
            </base-button>
          </form>
        </div>
        
        <!-- Security Settings -->
        <div class="bg-white rounded-lg shadow p-6">
          <h2 class="text-lg font-semibold mb-4">Configurações de Segurança</h2>
          
          <form @submit.prevent="updateSecuritySettings" class="space-y-4">
            <input-base
              v-model="securitySettings.session_timeout"
              label="Timeout de Sessão (minutos)"
              type="number"
              :error="errors.session_timeout"
            />
            <input-base
              v-model="securitySettings.max_login_attempts"
              label="Máximo de Tentativas de Login"
              type="number"
              :error="errors.max_login_attempts"
            />
            
            <div class="flex items-center gap-2">
              <input
                type="checkbox"
                id="two_factor_enabled"
                v-model="securitySettings.two_factor_enabled"
                class="checkbox checkbox-primary"
              />
              <label for="two_factor_enabled" class="text-sm">
                Habilitar Autenticação de Dois Fatores
              </label>
            </div>
            
            <div class="flex items-center gap-2">
              <input
                type="checkbox"
                id="force_https"
                v-model="securitySettings.force_https"
                class="checkbox checkbox-primary"
              />
              <label for="force_https" class="text-sm">
                Forçar HTTPS
              </label>
            </div>
            
            <base-button type="submit" :loading="loadingSecurity">
              Salvar Segurança
            </base-button>
          </form>
        </div>
      </div>
      
      <!-- System Information -->
      <div class="bg-white rounded-lg shadow p-6 mt-6">
        <h2 class="text-lg font-semibold mb-4">Informações do Sistema</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="text-center p-4 bg-gray-50 rounded">
            <div class="text-2xl font-bold text-primary">{{ systemInfo.version }}</div>
            <div class="text-sm text-gray-600">Versão do Sistema</div>
          </div>
          <div class="text-center p-4 bg-gray-50 rounded">
            <div class="text-2xl font-bold text-primary">{{ systemInfo.uptime }}</div>
            <div class="text-sm text-gray-600">Tempo Online</div>
          </div>
          <div class="text-center p-4 bg-gray-50 rounded">
            <div class="text-2xl font-bold text-primary">{{ systemInfo.users_count }}</div>
            <div class="text-sm text-gray-600">Usuários Ativos</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useToast } from "vue-toast-notification";
import { api } from "~/server/api";

const toast = useToast();
const loadingSystem = ref(false);
const loadingEmail = ref(false);
const loadingAnalytics = ref(false);
const loadingSecurity = ref(false);

const systemSettings = ref({
  site_name: 'Psy+ Admin',
  admin_email: '',
  site_description: '',
  maintenance_mode: false,
});

const emailSettings = ref({
  smtp_host: '',
  smtp_port: 587,
  smtp_username: '',
  smtp_password: '',
  smtp_encryption: true,
});

const analyticsSettings = ref({
  google_analytics_id: '',
  google_service_account: '',
  enabled: false,
});

const securitySettings = ref({
  session_timeout: 60,
  max_login_attempts: 5,
  two_factor_enabled: false,
  force_https: true,
});

const systemInfo = ref({
  version: '1.0.0',
  uptime: '0 days',
  users_count: 0,
});

const errors = ref({
  site_name: '',
  admin_email: '',
  site_description: '',
  smtp_host: '',
  smtp_port: '',
  smtp_username: '',
  smtp_password: '',
  google_analytics_id: '',
  google_service_account: '',
  session_timeout: '',
  max_login_attempts: '',
});

const fetchSettings = async () => {
  try {
    const { data } = await api.get('/admin/settings');
    systemSettings.value = { ...systemSettings.value, ...data.system };
    emailSettings.value = { ...emailSettings.value, ...data.email };
    analyticsSettings.value = { ...analyticsSettings.value, ...data.analytics };
    securitySettings.value = { ...securitySettings.value, ...data.security };
  } catch (error) {
    console.error('Error fetching settings:', error);
  }
};

const fetchSystemInfo = async () => {
  try {
    const { data } = await api.get('/admin/system-info');
    systemInfo.value = data;
  } catch (error) {
    console.error('Error fetching system info:', error);
  }
};

const updateSystemSettings = async () => {
  try {
    loadingSystem.value = true;
    await api.put('/admin/settings/system', systemSettings.value);
    toast.success('Configurações do sistema atualizadas!');
  } catch (error) {
    console.error('Error updating system settings:', error);
    toast.error('Erro ao atualizar configurações');
  } finally {
    loadingSystem.value = false;
  }
};

const updateEmailSettings = async () => {
  try {
    loadingEmail.value = true;
    await api.put('/admin/settings/email', emailSettings.value);
    toast.success('Configurações de email atualizadas!');
  } catch (error) {
    console.error('Error updating email settings:', error);
    toast.error('Erro ao atualizar configurações de email');
  } finally {
    loadingEmail.value = false;
  }
};

const updateAnalyticsSettings = async () => {
  try {
    loadingAnalytics.value = true;
    await api.put('/admin/settings/analytics', analyticsSettings.value);
    toast.success('Configurações de analytics atualizadas!');
  } catch (error) {
    console.error('Error updating analytics settings:', error);
    toast.error('Erro ao atualizar configurações de analytics');
  } finally {
    loadingAnalytics.value = false;
  }
};

const updateSecuritySettings = async () => {
  try {
    loadingSecurity.value = true;
    await api.put('/admin/settings/security', securitySettings.value);
    toast.success('Configurações de segurança atualizadas!');
  } catch (error) {
    console.error('Error updating security settings:', error);
    toast.error('Erro ao atualizar configurações de segurança');
  } finally {
    loadingSecurity.value = false;
  }
};

onMounted(() => {
  fetchSettings();
  fetchSystemInfo();
});
</script>
