<template>
  <div class="w-full">
    <div class="container-table pt-1 lg:h-sm">
      <div class="text-center py-20">
        <h1 class="text-3xl font-bold text-gray-800 mb-4">Analytics do Sistema</h1>
        <p class="text-gray-600 mb-8">O sistema de analytics foi reorganizado em páginas separadas para melhor navegação.</p>
        <div class="text-6xl mb-8">📊</div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-4xl mx-auto">
          <nuxt-link to="/analytics/overview" class="block p-6 bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="text-2xl mb-2">📈</div>
            <h3 class="font-semibold text-gray-800">V<PERSON>ão Geral</h3>
            <p class="text-sm text-gray-600">Estatísticas principais do sistema</p>
          </nuxt-link>

          <nuxt-link to="/analytics/usuarios" class="block p-6 bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="text-2xl mb-2">👥</div>
            <h3 class="font-semibold text-gray-800">Usuários</h3>
            <p class="text-sm text-gray-600">Analytics de usuários e atividade</p>
          </nuxt-link>

          <nuxt-link to="/analytics/agendamentos" class="block p-6 bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="text-2xl mb-2">📅</div>
            <h3 class="font-semibold text-gray-800">Agendamentos</h3>
            <p class="text-sm text-gray-600">Estatísticas de agendamentos</p>
          </nuxt-link>

          <nuxt-link to="/analytics/receita" class="block p-6 bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="text-2xl mb-2">💰</div>
            <h3 class="font-semibold text-gray-800">Receita</h3>
            <p class="text-sm text-gray-600">Analytics financeiros e receita</p>
          </nuxt-link>

          <nuxt-link to="/analytics/acesso" class="block p-6 bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="text-2xl mb-2">🔐</div>
            <h3 class="font-semibold text-gray-800">Acesso</h3>
            <p class="text-sm text-gray-600">Logs de acesso e segurança</p>
          </nuxt-link>

          <nuxt-link to="/analytics/bugs" class="block p-6 bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="text-2xl mb-2">🐛</div>
            <h3 class="font-semibold text-gray-800">Bugs</h3>
            <p class="text-sm text-gray-600">Relatórios de bugs e problemas</p>
          </nuxt-link>

          <nuxt-link to="/analytics/google" class="block p-6 bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="text-2xl mb-2">📊</div>
            <h3 class="font-semibold text-gray-800">Google Analytics</h3>
            <p class="text-sm text-gray-600">Integração com Google Analytics</p>
          </nuxt-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'professionals'
});
</script>
