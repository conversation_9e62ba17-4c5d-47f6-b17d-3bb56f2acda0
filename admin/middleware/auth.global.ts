// Import removed to fix ESLint warning
import { useLoginStore } from "~/store/user";

export default defineNuxtRouteMiddleware((to) => {
  const loginStore = useLoginStore();
  const isLoggedIn = loginStore.isLoggedIn;

  const guestRoutes = [
    "/login",
    "/criar-conta",
    "/forgot-password",
    "/recover-password",
    "/completar-cadastro",
    "/super-admin-login",
  ];

  // Handle root path early
  if (to.path === "/") {
    if (isLoggedIn) return; // Allow logged-in users
    return navigateTo("/login"); // Redirect others
  }

  // Remove trailing slash EXCEPT for root
  if (to.path.endsWith("/") && to.path !== "/") {
    const newPath = to.path.replace(/\/+$/, ""); // Trim all trailing slashes
    return navigateTo(newPath, { redirectCode: 301 });
  }

  // Auth checks
  if (!isLoggedIn && !guestRoutes.includes(to.path)) {
    return navigateTo("/login");
  }

  if (isLoggedIn && guestRoutes.includes(to.path)) {
    return navigateTo("/analytics");
  }
});
